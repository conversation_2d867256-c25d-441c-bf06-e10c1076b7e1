package com.hys.hm.infrastructure.persistence.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

/**
 * 框架自动配置类
 * 自动配置框架相关的Bean和设置
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@Slf4j
@Configuration
@EnableJpaRepositories(
    basePackages = {
        "com.hys.hm.infrastructure.*.repository"
    }
)
public class EncryptAutoConfiguration {
}

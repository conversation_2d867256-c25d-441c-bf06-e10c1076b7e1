package com.hys.hm.application.referral.service;

import com.hys.hm.domain.referral.model.ReferralForm;
import com.hys.hm.domain.referral.service.ReferralDomainService;
import com.hys.hm.domain.referral.service.ReferralQueryService;
import com.hys.hm.application.referral.converter.ReferralDTOConverter;
import com.hys.hm.application.referral.dto.ReferralCreateDTO;
import com.hys.hm.application.referral.dto.ReferralQueryDTO;
import com.hys.hm.shared.types.dto.ReferralSummaryDTO;
import com.hys.hm.shared.types.enums.ReferralStatus;
import com.hys.hm.shared.types.enums.UrgencyLevel;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 转诊表单服务测试类（新框架）
 * 演示如何测试使用新框架的服务类
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@ExtendWith(MockitoExtension.class)
class ReferralFormServiceNewTest {

    @Mock
    private ReferralDomainService referralDomainService;
    
    @Mock
    private ReferralQueryService referralQueryService;
    
    @Mock
    private ReferralDTOConverter dtoConverter;

    @InjectMocks
    private ReferralApplicationService referralFormService;

    private ReferralForm testReferral;
    private ReferralCreateDTO testCreateDTO;
    private ReferralQueryDTO testQueryDTO;

    @BeforeEach
    void setUp() {
        testReferral = createTestReferral();
        testCreateDTO = createTestCreateDTO();
        testQueryDTO = createTestQueryDTO();
    }

    @Test
    void testCreateReferralForm() {
        // Given
        String operatorId = "test-operator";
        when(dtoConverter.toReferralForm(testCreateDTO)).thenReturn(testReferral);
        when(referralDomainService.createReferralForm(testReferral, operatorId)).thenReturn(testReferral);
        when(dtoConverter.toQueryDTO(testReferral)).thenReturn(testQueryDTO);

        // When
        ReferralQueryDTO result = referralFormService.createReferralForm(testCreateDTO, operatorId);

        // Then
        assertNotNull(result);
        assertEquals(testQueryDTO.getId(), result.getId());
        assertEquals(testQueryDTO.getPatientName(), result.getPatientName());
        verify(dtoConverter).toReferralForm(testCreateDTO);
        verify(referralDomainService).createReferralForm(testReferral, operatorId);
        verify(dtoConverter).toQueryDTO(testReferral);
    }

    @Test
    void testGetReferralDetail() {
        // Given
        String referralId = "test-id";
        when(referralFormService.findById(referralId)).thenReturn(Optional.of(testReferral));
        when(dtoConverter.toQueryDTO(testReferral)).thenReturn(testQueryDTO);

        // When
        Optional<ReferralQueryDTO> result = referralFormService.getReferralDetail(referralId);

        // Then
        assertTrue(result.isPresent());
        assertEquals(testQueryDTO.getId(), result.get().getId());
        verify(dtoConverter).toQueryDTO(testReferral);
    }

    @Test
    void testGetReferralDetailByNo() {
        // Given
        String referralNo = "ZZ202507290001";
        String referralId = "test-id";
        
        // 创建一个ReferralSummaryDTO模拟对象
        ReferralSummaryDTO summaryDTO = new ReferralSummaryDTO();
        summaryDTO.setReferralId(referralId);
        summaryDTO.setReferralNo(referralNo);
        
        when(referralQueryService.getReferralSummaryByNo(referralNo)).thenReturn(Optional.of(summaryDTO));
        when(referralFormService.getReferralDetail(referralId)).thenReturn(Optional.of(testQueryDTO));

        // When
        Optional<ReferralQueryDTO> result = referralFormService.getReferralDetailByNo(referralNo);

        // Then
        assertTrue(result.isPresent());
        assertEquals(testQueryDTO.getId(), result.get().getId());
        verify(referralQueryService).getReferralSummaryByNo(referralNo);
        verify(referralFormService).getReferralDetail(referralId);
    }

    @Test
    void testFindByStatus() {
        // Given
        ReferralStatus status = ReferralStatus.PENDING;
        List<ReferralForm> referrals = Arrays.asList(testReferral);
        List<ReferralQueryDTO> queryDTOs = Arrays.asList(testQueryDTO);
        
        when(referralQueryService.findByStatus(status)).thenReturn(referrals);
        when(dtoConverter.toQueryDTOList(referrals)).thenReturn(queryDTOs);

        // When
        List<ReferralQueryDTO> result = referralFormService.findByStatus(status);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testQueryDTO.getId(), result.get(0).getId());
        verify(referralQueryService).findByStatus(status);
        verify(dtoConverter).toQueryDTOList(referrals);
    }

    @Test
    void testConfirmReferral() {
        // Given
        String referralId = "test-id";
        String operatorId = "operator-123";
        
        when(referralDomainService.confirmReferral(referralId, operatorId)).thenReturn(testReferral);
        when(dtoConverter.toQueryDTO(testReferral)).thenReturn(testQueryDTO);

        // When
        ReferralQueryDTO result = referralFormService.confirmReferral(referralId, operatorId);

        // Then
        assertNotNull(result);
        assertEquals(testQueryDTO.getId(), result.getId());
        verify(referralDomainService).confirmReferral(referralId, operatorId);
        verify(dtoConverter).toQueryDTO(testReferral);
    }

    @Test
    void testConfirmReferralNotFound() {
        // Given
        String id = "non-existent-id";
        String operatorId = "operator-123";
        when(referralFormRepository.findById(id)).thenReturn(Optional.empty());

        // When
        boolean result = referralFormService.confirmReferral(id, operatorId);

        // Then
        assertFalse(result);
        verify(referralFormRepository).findById(id);
        verify(referralFormRepository, never()).save(any());
    }

    @Test
    void testRejectReferral() {
        // Given
        String id = "test-id";
        String rejectReason = "医疗资源不足";
        String operatorId = "operator-123";
        when(referralFormRepository.findById(id)).thenReturn(Optional.of(testReferral));
        when(referralFormRepository.save(any(ReferralFormEntityNew.class))).thenReturn(testReferral);

        // When
        boolean result = referralFormService.rejectReferral(id, rejectReason, operatorId);

        // Then
        assertTrue(result);
        assertEquals(3, testReferral.getStatus()); // 已拒绝状态
        assertEquals(rejectReason, testReferral.getRejectReason());
        assertNotNull(testReferral.getConfirmTime());
        verify(referralFormRepository).findById(id);
        verify(referralFormRepository).save(testReferral);
    }

    @Test
    void testGenerateReferralNo() {
        // Given
        when(referralFormRepository.findMaxReferralNoByPrefix(anyString())).thenReturn(null);

        // When
        String referralNo = referralFormService.generateReferralNo();

        // Then
        assertNotNull(referralNo);
        assertTrue(referralNo.startsWith("ZZ"));
        assertEquals(14, referralNo.length()); // ZZ + 8位日期 + 4位序号
    }

    @Test
    void testIsReferralNoExists() {
        // Given
        String referralNo = "ZZ202507290001";
        when(referralFormRepository.existsByReferralNo(referralNo)).thenReturn(true);

        // When
        boolean exists = referralFormService.isReferralNoExists(referralNo);

        // Then
        assertTrue(exists);
        verify(referralFormRepository).existsByReferralNo(referralNo);
    }

    @Test
    void testCountByStatus() {
        // Given
        Integer status = 1;
        long expectedCount = 5L;
        when(referralFormRepository.countByStatus(status)).thenReturn(expectedCount);

        // When
        long count = referralFormService.countByStatus(status);

        // Then
        assertEquals(expectedCount, count);
        verify(referralFormRepository).countByStatus(status);
    }

    @Test
    void testBatchConfirmReferrals() {
        // Given
        List<String> ids = Arrays.asList("id1", "id2", "id3");
        String operatorId = "operator-123";
        
        // Mock每个ID的查找和保存
        for (String id : ids) {
            ReferralFormEntityNew referral = createTestReferral();
            referral.setId(id);
            when(referralFormRepository.findById(id)).thenReturn(Optional.of(referral));
            when(referralFormRepository.save(any(ReferralFormEntityNew.class))).thenReturn(referral);
        }

        // When
        int successCount = referralFormService.batchConfirmReferrals(ids, operatorId);

        // Then
        assertEquals(3, successCount);
        verify(referralFormRepository, times(3)).findById(anyString());
        verify(referralFormRepository, times(3)).save(any(ReferralFormEntityNew.class));
    }

    @Test
    void testValidate() {
        // Given
        ReferralFormEntityNew invalidReferral = new ReferralFormEntityNew();
        // 缺少必要字段

        // When & Then
        assertThrows(IllegalArgumentException.class, () -> {
            referralFormService.validate(invalidReferral);
        });
    }

    @Test
    void testValidateReferral() {
        // Given
        ReferralForm validReferral = createTestReferral();

        // When & Then
        assertDoesNotThrow(() -> {
            referralDomainService.validateReferralForm(validReferral);
        });
        
        verify(referralDomainService).validateReferralForm(validReferral);
    }

    @Test
    void testCreateReferralForm() {
        // Given
        ReferralForm referral = ReferralForm.builder()
            .basicInfoId("basic-123")
            .patientInfo(PatientInfo.builder()
                .patientId("basic-123")
                .name("测试患者")
                .gender(1)
                .age(30)
                .phone("13800138000")
                .build())
            .medicalInfo(MedicalInfo.builder()
                .referralReason("测试转诊")
                .build())
            .outHospital(HospitalInfo.builder()
                .unitId("out-unit-123")
                .unitName("转出医院")
                .build())
            .inHospital(HospitalInfo.builder()
                .unitId("in-unit-123")
                .unitName("转入医院")
                .build())
            .build();

        String operatorId = "operator-123";
        
        when(referralDomainService.createReferralForm(referral, operatorId)).thenReturn(testReferral);

        // When
        ReferralForm result = referralDomainService.createReferralForm(referral, operatorId);

        // Then
        assertNotNull(result);
        assertEquals(testReferral.getId(), result.getId());
        assertEquals(testReferral.getReferralNo(), result.getReferralNo());
        verify(referralDomainService).createReferralForm(referral, operatorId);
    }

    /**
     * 创建测试用的转诊表单领域模型
     */
    private ReferralForm createTestReferral() {
        return ReferralForm.builder()
            .id("test-referral-id")
            .basicInfoId("basic-info-123")
            .referralNo("ZZ202507290001")
            .referralDate(LocalDateTime.now())
            .patientName("张三")
            .gender(1)
            .age(45)
            .idCard("110101198001011234")
            .phone("13800138000")
            .address("北京市朝阳区")
            .referralReason("心脏病需要专科治疗")
            .outUnitId("out-hospital-123")
            .outUnitName("北京市第一医院")
            .inUnitId("in-hospital-456")
            .inUnitName("北京协和医院")
            .status(ReferralStatus.PENDING)
            .urgencyLevel(UrgencyLevel.NORMAL)
            .build();
    }
    
    /**
     * 创建测试用的转诊创建DTO
     */
    private ReferralCreateDTO createTestCreateDTO() {
        ReferralCreateDTO dto = new ReferralCreateDTO();
        dto.setBasicInfoId("basic-info-123");
        dto.setPatientName("张三");
        dto.setGender(1);
        dto.setAge(45);
        dto.setIdCard("110101198001011234");
        dto.setPhone("13800138000");
        dto.setAddress("北京市朝阳区");
        dto.setReferralReason("心脏病需要专科治疗");
        dto.setOutUnitId("out-hospital-123");
        dto.setOutUnitName("北京市第一医院");
        dto.setInUnitId("in-hospital-456");
        dto.setInUnitName("北京协和医院");
        dto.setUrgencyLevel(UrgencyLevel.NORMAL);
        return dto;
    }
    
    /**
     * 创建测试用的转诊查询DTO
     */
    private ReferralQueryDTO createTestQueryDTO() {
        ReferralQueryDTO dto = new ReferralQueryDTO();
        dto.setId("test-referral-id");
        dto.setBasicInfoId("basic-info-123");
        dto.setReferralNo("ZZ202507290001");
        dto.setReferralDate(LocalDateTime.now());
        dto.setPatientName("张三");
        dto.setGender(1);
        dto.setAge(45);
        dto.setIdCard("110101198001011234");
        dto.setPhone("13800138000");
        dto.setAddress("北京市朝阳区");
        dto.setReferralReason("心脏病需要专科治疗");
        dto.setOutUnitId("out-hospital-123");
        dto.setOutUnitName("北京市第一医院");
        dto.setInUnitId("in-hospital-456");
        dto.setInUnitName("北京协和医院");
        dto.setStatus(ReferralStatus.PENDING);
        dto.setUrgencyLevel(UrgencyLevel.NORMAL);
        return dto;
    }
}

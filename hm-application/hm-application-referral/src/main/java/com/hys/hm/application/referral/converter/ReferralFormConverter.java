package com.hys.hm.application.referral.converter;

import com.hys.hm.application.referral.dto.ReferralFormCreateDTO;
import com.hys.hm.application.referral.dto.ReferralFormQueryDTO;
import com.hys.hm.domain.referral.model.ReferralForm;
import com.hys.hm.domain.referral.enums.Gender;
import com.hys.hm.domain.referral.model.PatientInfo;
import com.hys.hm.domain.referral.model.MedicalInfo;
import com.hys.hm.domain.referral.model.HospitalInfo;
import com.hys.hm.shared.types.enums.ReferralStatus;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 转诊表单转换器
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-23
 */
@Component
public class ReferralFormConverter {
    
    /**
     * 创建DTO转换为值对象
     */
    public PatientInfo toPatientInfo(ReferralFormCreateDTO createDTO) {
        PatientInfo.Builder builder = PatientInfo.builder().name(createDTO.getPatientName())
                .gender(createDTO.getGender())
                .age(createDTO.getAge())
                .idCard(createDTO.getIdCard())
                .phone(createDTO.getPhone())
                .address(createDTO.getAddress())
                .addressDetail(createDTO.getAddressDetail())
                .fileNumber(createDTO.getFileNumber());
        return builder.build();
    }
    
    /**
     * 创建DTO转换为医疗信息
     */
    public MedicalInfo toMedicalInfo(ReferralFormCreateDTO createDTO) {

        return MedicalInfo.builder().preliminaryDiagnosis(createDTO.getImpression())
                .medicalHistory(createDTO.getMainHistory())
                .treatmentHistory(createDTO.getTreatmentProcess())
                .build();
    }
    
    /**
     * 创建DTO转换为转出医院信息
     */
    public HospitalInfo toOutHospitalInfo(ReferralFormCreateDTO createDTO) {



        return HospitalInfo.builder().unitId(createDTO.getOutUnitId()).unitName(createDTO.getOutUnitName())
                .doctorId(createDTO.getOutDoctorId()).doctorName(createDTO.getOutDoctorName())
                .doctorPhone(createDTO.getOutDoctorPhone()).deptId(createDTO.getOutDeptId()).deptName(createDTO.getOutDeptName()).build();
    }
    
    /**
     * 创建DTO转换为转入医院信息
     */
    public HospitalInfo toInHospitalInfo(ReferralFormCreateDTO createDTO) {

        return HospitalInfo.builder().unitId(createDTO.getInUnitId()).unitName(createDTO.getInUnitName())
                .doctorId(createDTO.getInDoctorId()).doctorName(createDTO.getInDoctorName())
                .doctorPhone(createDTO.getInDoctorPhone()).deptId(createDTO.getInDeptId()).deptName(createDTO.getInDeptName()).build();
    }
    
    /**
     * 领域模型转换为查询DTO
     */
    public ReferralFormQueryDTO toQueryDTO(ReferralForm referralForm) {
        if (referralForm == null) {
            return null;
        }
        
        ReferralFormQueryDTO queryDTO = new ReferralFormQueryDTO();
        
        // 基本信息
        queryDTO.setId(referralForm.getId());
        queryDTO.setBasicInfoId(referralForm.getBasicInfoId());
        queryDTO.setReferralNo(referralForm.getReferralNo());
        queryDTO.setReferralDate(referralForm.getReferralDate());
        queryDTO.setCreateTime(referralForm.getCreateTime());
        
        // 患者信息
        if (referralForm.getPatientInfo() != null) {
            PatientInfo patientInfo = referralForm.getPatientInfo();
            queryDTO.setPatientName(patientInfo.getName());
            queryDTO.setGender(patientInfo.getGender());
            queryDTO.setGenderDesc(patientInfo.getGenderDesc());
            queryDTO.setAge(patientInfo.getAge());
            queryDTO.setMaskedIdCard(maskIdCard(patientInfo.getIdCard()));
            queryDTO.setMaskedPhone(maskPhone(patientInfo.getPhone()));
            queryDTO.setFullAddress(getFullAddress(patientInfo.getAddress(), patientInfo.getAddressDetail()));
            queryDTO.setFileNumber(patientInfo.getFileNumber());
        }
        
        // 医疗信息
        if (referralForm.getMedicalInfo() != null) {
            MedicalInfo medicalInfo = referralForm.getMedicalInfo();
            queryDTO.setImpression(medicalInfo.getPreliminaryDiagnosis());
            queryDTO.setHistorySummary(getHistorySummary(medicalInfo));
            queryDTO.setHasTreatmentRecord(medicalInfo.hasTreatmentHistory());
        }
        
        // 转出医院信息
        if (referralForm.getOutHospital() != null) {
            HospitalInfo outHospital = referralForm.getOutHospital();
            queryDTO.setOutUnitId(outHospital.getUnitId());
            queryDTO.setOutHospitalInfo(outHospital.getFullDescription());
            queryDTO.setOutDoctorInfo(getDoctorInfo(outHospital.getDoctorName(), outHospital.getDoctorPhone()));
        }

        // 转入医院信息
        if (referralForm.getInHospital() != null) {
            HospitalInfo inHospital = referralForm.getInHospital();
            queryDTO.setInUnitId(inHospital.getUnitId());
            queryDTO.setInHospitalInfo(inHospital.getFullDescription());
            queryDTO.setInDoctorInfo(getDoctorInfo(inHospital.getDoctorName(), inHospital.getDoctorPhone()));
        }
        
        // 状态信息
        queryDTO.setStatus(referralForm.getStatus().getCode());
        queryDTO.setStatusDesc(referralForm.getStatus().getDescription());
        queryDTO.setRejectReason(referralForm.getRejectReason());
        queryDTO.setConfirmTime(referralForm.getConfirmTime());
        queryDTO.setCreateTime(referralForm.getCreateTime());

        // 业务状态
        setBusinessStatus(queryDTO, referralForm);

        return queryDTO;
    }
    
    /**
     * 领域模型列表转换为查询DTO列表
     */
    public List<ReferralFormQueryDTO> toQueryDTOList(List<ReferralForm> referralForms) {
        if (referralForms == null) {
            return null;
        }

        return referralForms.stream()
                .map(this::toQueryDTO)
                .collect(Collectors.toList());
    }

    /**
     * 脱敏身份证号
     */
    private String maskIdCard(String idCard) {
        if (idCard == null || idCard.length() < 8) {
            return idCard;
        }
        return idCard.substring(0, 4) + "**********" + idCard.substring(idCard.length() - 4);
    }

    /**
     * 脱敏手机号
     */
    private String maskPhone(String phone) {
        if (phone == null || phone.length() < 7) {
            return phone;
        }

        // 对于11位手机号（标准格式），显示前3位和后4位
        if (phone.length() == 11) {
            return phone.substring(0, 3) + "****" + phone.substring(7);
        }

        // 对于其他长度的手机号，显示前3位，其余用星号遮挡
        if (phone.length() <= 7) {
            return phone.substring(0, 3) + "****";
        } else {
            // 长度大于7但不等于11的情况，显示前3位和后面部分
            return phone.substring(0, 3) + "****" + phone.substring(phone.length() - 4);
        }
    }

    /**
     * 获取完整地址
     */
    private String getFullAddress(String address, String addressDetail) {
        if (address == null && addressDetail == null) {
            return null;
        }
        if (address == null) {
            return addressDetail;
        }
        if (addressDetail == null) {
            return address;
        }
        return address + " " + addressDetail;
    }

    /**
     * 获取病史摘要
     */
    private String getHistorySummary(MedicalInfo medicalInfo) {
        StringBuilder summary = new StringBuilder();

        if (medicalInfo.getMainSymptoms() != null && !medicalInfo.getMainSymptoms().trim().isEmpty()) {
            summary.append("主要症状: ").append(medicalInfo.getMainSymptoms());
        }

        if (medicalInfo.getMedicalHistory() != null && !medicalInfo.getMedicalHistory().trim().isEmpty()) {
            if (summary.length() > 0) {
                summary.append("; ");
            }
            summary.append("病史: ").append(medicalInfo.getMedicalHistory());
        }

        return summary.toString();
    }

    /**
     * 获取医生信息
     */
    private String getDoctorInfo(String doctorName, String doctorPhone) {
        if (doctorName == null) {
            return null;
        }

        StringBuilder info = new StringBuilder(doctorName);

        if (doctorPhone != null && !doctorPhone.trim().isEmpty()) {
            info.append(" (").append(doctorPhone).append(")");
        }

        return info.toString();
    }

    /**
     * 设置业务状态
     */
    private void setBusinessStatus(ReferralFormQueryDTO queryDTO, ReferralForm referralForm) {
        ReferralStatus status = referralForm.getStatus();

        queryDTO.setIsPending(ReferralStatus.PENDING.equals(status));
        queryDTO.setIsConfirmed(ReferralStatus.CONFIRMED.equals(status));
        queryDTO.setIsCompleted(ReferralStatus.COMPLETED.equals(status));
        queryDTO.setIsRejected(ReferralStatus.REJECTED.equals(status));
        queryDTO.setIsCancelled(ReferralStatus.CANCELLED.equals(status));
        queryDTO.setIsTimeout(false); // 暂时设为false，后续可以根据业务逻辑计算

        // 设置转诊描述
        String description = buildReferralDescription(referralForm);
        queryDTO.setReferralDescription(description);
    }

    /**
     * 构建转诊描述
     */
    private String buildReferralDescription(ReferralForm referralForm) {
        StringBuilder desc = new StringBuilder();

        if (referralForm.getOutHospital() != null && referralForm.getInHospital() != null) {
            desc.append("从 ");
            if (referralForm.getOutHospital().getUnitName() != null) {
                desc.append(referralForm.getOutHospital().getUnitName());
            }
            desc.append(" 转诊到 ");
            if (referralForm.getInHospital().getUnitName() != null) {
                desc.append(referralForm.getInHospital().getUnitName());
            }
        }

        return desc.toString();
    }
}

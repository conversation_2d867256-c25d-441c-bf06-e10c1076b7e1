<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.hys</groupId>
    <artifactId>hm-application</artifactId>
    <version>3.0.1-SNAPSHOT</version>
  </parent>
  <groupId>com.hys</groupId>
  <artifactId>hm-application-referral</artifactId>
  <version>3.0.1-SNAPSHOT</version>
  <name>hm-application-referral</name>
  <description>转诊管理应用服务模块</description>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <dependencies>
    <dependency>
      <groupId>com.hys</groupId>
      <artifactId>hm-domain-referral</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.hys</groupId>
      <artifactId>hm-domain-patient</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.hys</groupId>
      <artifactId>hm-infrastructure-persistence-referral</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.hys</groupId>
      <artifactId>hm-domain-health</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.hys</groupId>
      <artifactId>hm-shared-types</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.hys</groupId>
      <artifactId>hm-shared-common</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.hys</groupId>
      <artifactId>hm-shared-framework</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-validation</artifactId>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>
</project>
